/* 组件样式 */

/* 按钮组件 */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-fast);
  text-decoration: none;
  gap: 6px;
  white-space: nowrap;
}

.btn:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.btn:active {
  transform: translateY(0);
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* 按钮变体 */
.btn-primary {
  background-color: var(--primary-color);
  color: var(--text-white);
}

.btn-primary:hover {
  background-color: #1d4ed8;
}

.btn-secondary {
  background-color: var(--text-secondary);
  color: var(--text-white);
}

.btn-secondary:hover {
  background-color: #4b5563;
}

.btn-success {
  background-color: var(--success-color);
  color: var(--text-white);
}

.btn-success:hover {
  background-color: #059669;
}

.btn-warning {
  background-color: var(--warning-color);
  color: var(--text-white);
}

.btn-warning:hover {
  background-color: #d97706;
}

.btn-danger {
  background-color: var(--danger-color);
  color: var(--text-white);
}

.btn-danger:hover {
  background-color: #dc2626;
}

.btn-outline {
  background-color: transparent;
  border: 1px solid var(--border-color);
  color: var(--text-primary);
}

.btn-outline:hover {
  background-color: var(--background-color);
  border-color: var(--border-hover);
}

.btn-icon {
  padding: 8px;
  border-radius: 50%;
  background-color: transparent;
  border: none;
  cursor: pointer;
  transition: background-color var(--transition-fast);
  font-size: 16px;
}

.btn-icon:hover {
  background-color: var(--background-color);
}

.btn-close {
  padding: 4px;
  border: none;
  background: none;
  cursor: pointer;
  font-size: 16px;
  color: var(--text-secondary);
  transition: color var(--transition-fast);
}

.btn-close:hover {
  color: var(--text-primary);
}

/* 卡片组件 */
.card {
  background-color: var(--content-color);
  border-radius: 8px;
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-color);
  overflow: hidden;
}

.card-header {
  padding: 16px 20px;
  border-bottom: 1px solid var(--border-color);
  background-color: var(--background-color);
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  margin: 0;
}

.card-body {
  padding: 20px;
}

.card-footer {
  padding: 16px 20px;
  border-top: 1px solid var(--border-color);
  background-color: var(--background-color);
}

/* 表格组件 */
.table {
  width: 100%;
  border-collapse: collapse;
  background-color: var(--content-color);
  border-radius: 8px;
  overflow: hidden;
  box-shadow: var(--shadow-sm);
}

.table th,
.table td {
  padding: 12px 16px;
  text-align: left;
  border-bottom: 1px solid var(--border-color);
}

.table th {
  background-color: var(--background-color);
  font-weight: 600;
  color: var(--text-primary);
  font-size: 13px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.table tbody tr:hover {
  background-color: var(--background-color);
}

.table tbody tr:last-child td {
  border-bottom: none;
}

/* 表单组件 */
.form-group {
  margin-bottom: 16px;
}

.form-label {
  display: block;
  margin-bottom: 6px;
  font-weight: 500;
  color: var(--text-primary);
}

.form-control {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid var(--border-color);
  border-radius: 6px;
  font-size: 14px;
  transition: border-color var(--transition-fast);
}

.form-control:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.form-control:disabled {
  background-color: var(--background-color);
  color: var(--text-muted);
  cursor: not-allowed;
}

.form-select {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 8px center;
  background-repeat: no-repeat;
  background-size: 16px 12px;
  padding-right: 32px;
}

/* 状态指示器 */
.status {
  display: inline-flex;
  align-items: center;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  gap: 4px;
}

.status-online {
  background-color: rgba(16, 185, 129, 0.1);
  color: var(--success-color);
}

.status-offline {
  background-color: rgba(239, 68, 68, 0.1);
  color: var(--danger-color);
}

.status-warning {
  background-color: rgba(245, 158, 11, 0.1);
  color: var(--warning-color);
}

.status-running {
  background-color: rgba(59, 130, 246, 0.1);
  color: var(--info-color);
}

/* 进度条 */
.progress {
  width: 100%;
  height: 8px;
  background-color: var(--border-color);
  border-radius: 4px;
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  background-color: var(--primary-color);
  transition: width var(--transition-normal);
}

.progress-bar.success {
  background-color: var(--success-color);
}

.progress-bar.warning {
  background-color: var(--warning-color);
}

.progress-bar.danger {
  background-color: var(--danger-color);
}

/* 徽章 */
.badge {
  display: inline-flex;
  align-items: center;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.badge-primary {
  background-color: var(--primary-color);
  color: var(--text-white);
}

.badge-success {
  background-color: var(--success-color);
  color: var(--text-white);
}

.badge-warning {
  background-color: var(--warning-color);
  color: var(--text-white);
}

.badge-danger {
  background-color: var(--danger-color);
  color: var(--text-white);
}

.badge-secondary {
  background-color: var(--text-secondary);
  color: var(--text-white);
}

/* 模态框 */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.modal-dialog {
  background-color: var(--content-color);
  border-radius: 8px;
  box-shadow: var(--shadow-lg);
  max-width: 500px;
  width: 90%;
  max-height: 90%;
  overflow: hidden;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid var(--border-color);
}

.modal-title {
  font-size: 18px;
  font-weight: 600;
  margin: 0;
}

.modal-body {
  padding: 20px;
  max-height: 400px;
  overflow-y: auto;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 20px;
  border-top: 1px solid var(--border-color);
}

/* 通知 */
.notification {
  position: fixed;
  top: 20px;
  right: 20px;
  background-color: var(--content-color);
  border-radius: 8px;
  box-shadow: var(--shadow-lg);
  padding: 16px 20px;
  border-left: 4px solid var(--primary-color);
  max-width: 400px;
  z-index: 10000;
  animation: slideIn 0.3s ease;
}

.notification.success {
  border-left-color: var(--success-color);
}

.notification.warning {
  border-left-color: var(--warning-color);
}

.notification.error {
  border-left-color: var(--danger-color);
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* 工具提示 */
.tooltip {
  position: relative;
  cursor: help;
}

.tooltip::after {
  content: attr(data-tooltip);
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background-color: var(--text-primary);
  color: var(--text-white);
  padding: 6px 8px;
  border-radius: 4px;
  font-size: 12px;
  white-space: nowrap;
  opacity: 0;
  visibility: hidden;
  transition: all var(--transition-fast);
  z-index: 1000;
}

.tooltip:hover::after {
  opacity: 1;
  visibility: visible;
}

/* 仪表盘样式 */
.dashboard {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.section-title {
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 16px;
  color: var(--text-primary);
}

/* 统计卡片 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

.stat-card {
  background: var(--content-color);
  border-radius: 12px;
  padding: 20px;
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  gap: 16px;
  transition: transform var(--transition-fast);
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.stat-icon {
  font-size: 32px;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--background-color);
  border-radius: 12px;
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 28px;
  font-weight: 700;
  color: var(--text-primary);
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  color: var(--text-secondary);
  margin: 4px 0;
}

.stat-change {
  font-size: 12px;
  font-weight: 500;
  padding: 2px 6px;
  border-radius: 4px;
}

.stat-change.positive {
  color: var(--success-color);
  background: rgba(16, 185, 129, 0.1);
}

.stat-change.negative {
  color: var(--danger-color);
  background: rgba(239, 68, 68, 0.1);
}

.stat-change.running {
  color: var(--info-color);
  background: rgba(59, 130, 246, 0.1);
}

/* 图表区域 */
.chart-row {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 20px;
}

.chart-card {
  background: var(--content-color);
  border-radius: 12px;
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-color);
  overflow: hidden;
}

.chart-container {
  height: 300px;
  padding: 20px;
}

.chart-placeholder {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--background-color);
  border-radius: 8px;
  border: 2px dashed var(--border-color);
}

.trend-summary {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.trend-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: var(--content-color);
  border-radius: 8px;
  box-shadow: var(--shadow-sm);
}

.trend-label {
  font-weight: 500;
  color: var(--text-secondary);
}

.trend-value {
  font-weight: 600;
  font-size: 16px;
}

.trend-value.success {
  color: var(--success-color);
}

.trend-value.danger {
  color: var(--danger-color);
}

.trend-value.info {
  color: var(--info-color);
}

/* 任务监控 */
.task-monitor {
  display: flex;
  flex-direction: column;
  gap: 12px;
  max-height: 300px;
  overflow-y: auto;
}

.task-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: var(--background-color);
  border-radius: 8px;
  border-left: 4px solid var(--border-color);
}

.task-item.completed {
  border-left-color: var(--success-color);
}

.task-item.running {
  border-left-color: var(--info-color);
}

.task-item.pending {
  border-left-color: var(--warning-color);
}

.task-item.failed {
  border-left-color: var(--danger-color);
}

.task-status {
  font-size: 18px;
}

.task-info {
  flex: 1;
}

.task-name {
  font-weight: 500;
  color: var(--text-primary);
}

.task-progress {
  font-size: 12px;
  color: var(--text-secondary);
}

.task-time {
  font-size: 12px;
  color: var(--text-muted);
  font-family: monospace;
}

/* 警报区域 */
.alert-card {
  background: var(--content-color);
  border-radius: 12px;
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-color);
  overflow: hidden;
}

.alert-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.alert-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: var(--background-color);
  border-radius: 8px;
  border-left: 4px solid var(--border-color);
}

.alert-item.high {
  border-left-color: var(--danger-color);
  background: rgba(239, 68, 68, 0.05);
}

.alert-item.medium {
  border-left-color: var(--warning-color);
  background: rgba(245, 158, 11, 0.05);
}

.alert-item.low {
  border-left-color: var(--info-color);
  background: rgba(59, 130, 246, 0.05);
}

.alert-icon {
  font-size: 20px;
}

.alert-content {
  flex: 1;
}

.alert-title {
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: 4px;
}

.alert-time {
  font-size: 12px;
  color: var(--text-secondary);
}

.alert-actions {
  display: flex;
  gap: 8px;
}

/* 快捷操作 */
.action-card {
  background: var(--content-color);
  border-radius: 12px;
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-color);
  overflow: hidden;
}

.action-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 16px;
}

.action-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 20px;
  background: var(--background-color);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  cursor: pointer;
  transition: all var(--transition-fast);
  text-decoration: none;
  color: var(--text-primary);
}

.action-btn:hover {
  background: var(--content-color);
  border-color: var(--primary-color);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.action-icon {
  font-size: 24px;
}

.action-text {
  font-size: 14px;
  font-weight: 500;
  text-align: center;
}

/* 主机管理页面样式 */
.hosts-page {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  font-size: 24px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.page-actions {
  display: flex;
  gap: 12px;
}

.hosts-filters {
  display: flex;
  gap: 20px;
  align-items: end;
  padding: 20px;
  background: var(--content-color);
  border-radius: 8px;
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-color);
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.filter-group label {
  font-size: 13px;
  font-weight: 500;
  color: var(--text-secondary);
}

.hosts-table-container {
  background: var(--content-color);
  border-radius: 8px;
  overflow: hidden;
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-color);
}

.host-name {
  display: flex;
  align-items: center;
  gap: 8px;
}

.host-name .name {
  font-weight: 500;
  color: var(--text-primary);
}

.action-buttons {
  display: flex;
  gap: 6px;
}

.table-pagination {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: var(--content-color);
  border-top: 1px solid var(--border-color);
}

.pagination-info {
  font-size: 13px;
  color: var(--text-secondary);
}

.pagination-controls {
  display: flex;
  gap: 8px;
}

/* 规则管理页面样式 */
.rules-page {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.rules-stats {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  background: var(--content-color);
  border-radius: 8px;
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-color);
}

.stat-label {
  font-size: 14px;
  color: var(--text-secondary);
}

.stat-value {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
}

.stat-value.success {
  color: var(--success-color);
}

.stat-value.danger {
  color: var(--danger-color);
}

.rules-filters {
  display: flex;
  gap: 20px;
  align-items: end;
  padding: 20px;
  background: var(--content-color);
  border-radius: 8px;
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-color);
}

.rules-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 20px;
}

.rule-card {
  background: var(--content-color);
  border-radius: 12px;
  padding: 20px;
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-color);
  transition: all var(--transition-fast);
}

.rule-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.rule-card.disabled {
  opacity: 0.6;
  background: var(--background-color);
}

.rule-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.rule-title h4 {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 4px 0;
}

.rule-id {
  font-size: 12px;
  color: var(--text-muted);
  font-family: monospace;
}

.rule-toggle .switch {
  position: relative;
  display: inline-block;
  width: 44px;
  height: 24px;
}

.rule-toggle .switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.rule-toggle .slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--border-color);
  transition: 0.3s;
  border-radius: 24px;
}

.rule-toggle .slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 3px;
  bottom: 3px;
  background-color: white;
  transition: 0.3s;
  border-radius: 50%;
}

.rule-toggle input:checked + .slider {
  background-color: var(--primary-color);
}

.rule-toggle input:checked + .slider:before {
  transform: translateX(20px);
}

.rule-meta {
  display: flex;
  gap: 8px;
  margin-bottom: 12px;
}

.rule-description {
  font-size: 14px;
  color: var(--text-secondary);
  line-height: 1.5;
  margin-bottom: 16px;
}

.rule-actions {
  display: flex;
  gap: 8px;
}

/* 任务管理页面样式 */
.tasks-page {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.tasks-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 20px;
}

.tasks-filters {
  display: flex;
  gap: 20px;
  align-items: end;
  padding: 20px;
  background: var(--content-color);
  border-radius: 8px;
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-color);
}

.tasks-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.task-card {
  background: var(--content-color);
  border-radius: 12px;
  padding: 20px;
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-color);
  transition: all var(--transition-fast);
}

.task-card:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.task-card.running {
  border-left: 4px solid var(--info-color);
}

.task-card.completed {
  border-left: 4px solid var(--success-color);
}

.task-card.pending {
  border-left: 4px solid var(--warning-color);
}

.task-card.failed {
  border-left: 4px solid var(--danger-color);
}

.task-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.task-info h4 {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 8px 0;
}

.task-meta {
  display: flex;
  gap: 16px;
  font-size: 13px;
  color: var(--text-secondary);
}

.task-id {
  font-family: monospace;
  background: var(--background-color);
  padding: 2px 6px;
  border-radius: 4px;
}

.task-created {
  color: var(--text-muted);
}

.status-badge {
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.status-badge.running {
  background: rgba(59, 130, 246, 0.1);
  color: var(--info-color);
}

.status-badge.completed {
  background: rgba(16, 185, 129, 0.1);
  color: var(--success-color);
}

.status-badge.pending {
  background: rgba(245, 158, 11, 0.1);
  color: var(--warning-color);
}

.status-badge.failed {
  background: rgba(239, 68, 68, 0.1);
  color: var(--danger-color);
}

.task-details {
  margin-bottom: 16px;
}

.task-stats {
  display: flex;
  gap: 20px;
  margin-bottom: 12px;
}

.task-stats .stat-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
  color: var(--text-secondary);
}

.task-stats .stat-icon {
  font-size: 16px;
}

.task-progress {
  margin-top: 12px;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 6px;
}

.progress-label {
  font-size: 13px;
  color: var(--text-secondary);
}

.progress-value {
  font-size: 13px;
  font-weight: 500;
  color: var(--text-primary);
}

.task-actions {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

/* 报告中心页面样式 */
.reports-page {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.reports-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 20px;
}

.reports-filters {
  display: flex;
  gap: 20px;
  align-items: end;
  padding: 20px;
  background: var(--content-color);
  border-radius: 8px;
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-color);
}

.reports-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 20px;
}

.report-card {
  background: var(--content-color);
  border-radius: 12px;
  padding: 20px;
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-color);
  transition: all var(--transition-fast);
}

.report-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.report-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.report-title h4 {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 4px 0;
}

.report-id {
  font-size: 12px;
  color: var(--text-muted);
  font-family: monospace;
}

.report-score {
  font-size: 24px;
  font-weight: 700;
  padding: 8px 12px;
  border-radius: 8px;
  text-align: center;
  min-width: 80px;
}

.report-score.excellent {
  background: rgba(16, 185, 129, 0.1);
  color: var(--success-color);
}

.report-score.good {
  background: rgba(59, 130, 246, 0.1);
  color: var(--info-color);
}

.report-score.fair {
  background: rgba(245, 158, 11, 0.1);
  color: var(--warning-color);
}

.report-score.poor {
  background: rgba(239, 68, 68, 0.1);
  color: var(--danger-color);
}

.report-meta {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 16px;
}

.meta-item {
  display: flex;
  justify-content: space-between;
  font-size: 13px;
}

.meta-label {
  color: var(--text-secondary);
}

.meta-value {
  color: var(--text-primary);
  font-weight: 500;
}

.report-preview {
  display: flex;
  gap: 20px;
  align-items: center;
  margin-bottom: 16px;
  padding: 16px;
  background: var(--background-color);
  border-radius: 8px;
}

.score-chart {
  position: relative;
}

.score-circle {
  position: relative;
}

.score-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 14px;
  font-weight: 600;
  color: var(--text-primary);
}

.score-breakdown {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.breakdown-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 13px;
}

.breakdown-label {
  color: var(--text-secondary);
}

.breakdown-value {
  font-weight: 500;
}

.breakdown-value.success {
  color: var(--success-color);
}

.breakdown-value.danger {
  color: var(--danger-color);
}

.breakdown-value.warning {
  color: var(--warning-color);
}

.report-actions {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

/* 系统设置页面样式 */
.settings-page {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.settings-content {
  display: flex;
  gap: 20px;
  min-height: 600px;
}

.settings-nav {
  width: 200px;
  background: var(--content-color);
  border-radius: 8px;
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-color);
  padding: 8px;
}

.settings-nav .nav-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  border-radius: 6px;
  cursor: pointer;
  transition: all var(--transition-fast);
  margin-bottom: 4px;
}

.settings-nav .nav-item:hover {
  background: var(--background-color);
}

.settings-nav .nav-item.active {
  background: var(--primary-color);
  color: var(--text-white);
}

.settings-nav .nav-icon {
  font-size: 16px;
}

.settings-nav .nav-text {
  font-size: 14px;
  font-weight: 500;
}

.settings-panels {
  flex: 1;
  background: var(--content-color);
  border-radius: 8px;
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-color);
  overflow: hidden;
}

.settings-panel {
  display: none;
  padding: 24px;
}

.settings-panel.active {
  display: block;
}

.panel-content h3 {
  font-size: 20px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 8px 0;
}

.panel-description {
  font-size: 14px;
  color: var(--text-secondary);
  margin-bottom: 24px;
  line-height: 1.5;
}

.checkbox-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: var(--text-primary);
  cursor: pointer;
}

.checkbox-label input[type="checkbox"] {
  width: 16px;
  height: 16px;
  accent-color: var(--primary-color);
}

.form-text {
  font-size: 12px;
  color: var(--text-muted);
  margin-top: 4px;
}

/* 关于页面样式 */
.about-section {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.app-info {
  display: flex;
  gap: 16px;
  align-items: center;
  padding: 20px;
  background: var(--background-color);
  border-radius: 8px;
}

.app-icon {
  font-size: 48px;
}

.app-details h3 {
  font-size: 24px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 8px 0;
}

.version {
  font-size: 14px;
  color: var(--text-secondary);
  margin-bottom: 8px;
}

.description {
  font-size: 14px;
  color: var(--text-secondary);
  line-height: 1.5;
}

.system-info h4 {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 12px 0;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  padding: 8px 12px;
  background: var(--background-color);
  border-radius: 6px;
  font-size: 13px;
}

.info-label {
  color: var(--text-secondary);
}

.info-value {
  color: var(--text-primary);
  font-weight: 500;
}

.actions-section h4 {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 12px 0;
}

.action-buttons {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.copyright {
  text-align: center;
  padding-top: 20px;
  border-top: 1px solid var(--border-color);
}

.copyright p {
  font-size: 12px;
  color: var(--text-muted);
  margin: 0;
}

/* 主题选择器样式 */
.theme-selector {
  margin-top: 8px;
}

.theme-options {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
  gap: 12px;
}

.theme-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 16px 12px;
  border: 2px solid var(--border-color);
  border-radius: 8px;
  cursor: pointer;
  transition: all var(--transition-fast);
  background: var(--content-color);
}

.theme-option:hover {
  border-color: var(--primary-color);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.theme-option.active {
  border-color: var(--primary-color);
  background: rgba(37, 99, 235, 0.05);
}

.theme-preview {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: 2px solid var(--border-color);
  position: relative;
  overflow: hidden;
}

.theme-preview.light {
  background: linear-gradient(135deg, #ffffff 50%, #f8fafc 50%);
}

.theme-preview.dark {
  background: linear-gradient(135deg, #111827 50%, #1f2937 50%);
}

.theme-preview.blue {
  background: linear-gradient(135deg, #1e40af 50%, #eff6ff 50%);
}

.theme-preview.green {
  background: linear-gradient(135deg, #059669 50%, #ecfdf5 50%);
}

.theme-preview.purple {
  background: linear-gradient(135deg, #7c3aed 50%, #faf5ff 50%);
}

.theme-name {
  font-size: 12px;
  font-weight: 500;
  color: var(--text-primary);
  text-align: center;
}

/* 个性化设置样式 */
.personalization-section {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.setting-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background: var(--background-color);
  border-radius: 8px;
  border: 1px solid var(--border-color);
}

.setting-info {
  flex: 1;
}

.setting-title {
  font-size: 14px;
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: 4px;
}

.setting-description {
  font-size: 12px;
  color: var(--text-secondary);
  line-height: 1.4;
}

.setting-control {
  margin-left: 16px;
}

/* 颜色选择器 */
.color-picker {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.color-option {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  border: 2px solid var(--border-color);
  cursor: pointer;
  transition: all var(--transition-fast);
  position: relative;
}

.color-option:hover {
  transform: scale(1.1);
  box-shadow: var(--shadow-md);
}

.color-option.active::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-weight: bold;
  font-size: 14px;
  text-shadow: 0 0 2px rgba(0,0,0,0.5);
}

.color-option.primary { background-color: #2563eb; }
.color-option.success { background-color: #10b981; }
.color-option.warning { background-color: #f59e0b; }
.color-option.danger { background-color: #ef4444; }
.color-option.purple { background-color: #8b5cf6; }
.color-option.pink { background-color: #ec4899; }
.color-option.indigo { background-color: #6366f1; }
.color-option.teal { background-color: #14b8a6; }

/* 报告详情模态框样式 */
.report-detail {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.report-summary {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  padding: 20px;
  background: var(--background-color);
  border-radius: 8px;
}

.summary-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.summary-label {
  font-size: 13px;
  color: var(--text-secondary);
}

.summary-value {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
}

.summary-value.score-excellent {
  color: var(--success-color);
}

.summary-value.score-good {
  color: var(--info-color);
}

.summary-value.score-fair {
  color: var(--warning-color);
}

.summary-value.score-poor {
  color: var(--danger-color);
}

.report-charts {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.chart-section h4 {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 12px 0;
}

.result-distribution {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.result-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: var(--background-color);
  border-radius: 6px;
}

.result-icon {
  font-size: 18px;
}

.result-label {
  flex: 1;
  font-weight: 500;
  color: var(--text-primary);
}

.result-count {
  font-weight: 600;
  color: var(--text-secondary);
}

.risk-distribution {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.risk-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 12px;
  border-radius: 6px;
  font-size: 14px;
}

.risk-item.high {
  background: rgba(239, 68, 68, 0.1);
  color: var(--danger-color);
}

.risk-item.medium {
  background: rgba(245, 158, 11, 0.1);
  color: var(--warning-color);
}

.risk-item.low {
  background: rgba(59, 130, 246, 0.1);
  color: var(--info-color);
}

.risk-label {
  font-weight: 500;
}

.risk-count {
  font-weight: 600;
}

.report-recommendations h4 {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 12px 0;
}

.recommendation-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.recommendation-list li {
  padding: 8px 0;
  border-bottom: 1px solid var(--border-color);
  color: var(--text-secondary);
  line-height: 1.5;
}

.recommendation-list li:last-child {
  border-bottom: none;
}

.recommendation-list li:before {
  content: "💡 ";
  margin-right: 8px;
}

/* 规则详情模态框样式 */
.rule-detail {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.detail-section {
  border-bottom: 1px solid var(--border-color);
  padding-bottom: 20px;
}

.detail-section:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.detail-section h4 {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 16px 0;
}

.detail-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
}

.detail-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.detail-label {
  font-size: 13px;
  color: var(--text-secondary);
  font-weight: 500;
}

.detail-value {
  font-size: 14px;
  color: var(--text-primary);
}

.config-item {
  margin-bottom: 16px;
}

.config-label {
  display: block;
  font-size: 13px;
  color: var(--text-secondary);
  font-weight: 500;
  margin-bottom: 8px;
}

.config-value {
  background: var(--background-color);
  border-radius: 6px;
  padding: 12px;
}

.config-value pre {
  margin: 0;
  font-family: 'Courier New', monospace;
  font-size: 13px;
  color: var(--text-primary);
  white-space: pre-wrap;
  word-break: break-all;
}

.config-value code {
  font-family: 'Courier New', monospace;
  font-size: 13px;
  color: var(--text-primary);
  background: none;
  padding: 0;
}

.rule-description {
  font-size: 14px;
  color: var(--text-secondary);
  line-height: 1.6;
  margin: 0;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 16px;
}

.stat-box {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16px;
  background: var(--background-color);
  border-radius: 8px;
  text-align: center;
}

.stat-number {
  font-size: 24px;
  font-weight: 700;
  color: var(--primary-color);
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  color: var(--text-secondary);
  font-weight: 500;
}

/* 实时监控样式 */
.real-time-info {
  margin-top: 12px;
  padding: 12px;
  background: rgba(59, 130, 246, 0.05);
  border-radius: 6px;
  border-left: 3px solid var(--info-color);
}

.current-host {
  font-size: 13px;
  font-weight: 500;
  color: var(--info-color);
  margin-bottom: 4px;
}

.task-message {
  font-size: 12px;
  color: var(--text-secondary);
}

/* 实时状态指示器 */
.status-indicator {
  position: relative;
  display: inline-block;
}

.status-indicator.online::before {
  content: '';
  position: absolute;
  top: -2px;
  right: -2px;
  width: 8px;
  height: 8px;
  background: var(--success-color);
  border-radius: 50%;
  animation: pulse-green 2s infinite;
}

.status-indicator.running::before {
  content: '';
  position: absolute;
  top: -2px;
  right: -2px;
  width: 8px;
  height: 8px;
  background: var(--info-color);
  border-radius: 50%;
  animation: pulse-blue 2s infinite;
}

.status-indicator.error::before {
  content: '';
  position: absolute;
  top: -2px;
  right: -2px;
  width: 8px;
  height: 8px;
  background: var(--danger-color);
  border-radius: 50%;
  animation: pulse-red 2s infinite;
}

@keyframes pulse-green {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.5;
    transform: scale(1.2);
  }
}

@keyframes pulse-blue {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.5;
    transform: scale(1.2);
  }
}

@keyframes pulse-red {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.5;
    transform: scale(1.2);
  }
}

/* 连接状态指示器 */
.connection-status {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  padding: 4px 8px;
  border-radius: 12px;
  font-weight: 500;
}

.connection-status.connected {
  background: rgba(16, 185, 129, 0.1);
  color: var(--success-color);
}

.connection-status.disconnected {
  background: rgba(239, 68, 68, 0.1);
  color: var(--danger-color);
}

.connection-status.connecting {
  background: rgba(245, 158, 11, 0.1);
  color: var(--warning-color);
}

/* 数据更新时间戳 */
.last-updated {
  font-size: 11px;
  color: var(--text-muted);
  font-style: italic;
}

/* 实时数据表格 */
.realtime-table {
  position: relative;
}

.realtime-table.updating::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, transparent, var(--primary-color), transparent);
  animation: loading-bar 1s ease-in-out;
}

@keyframes loading-bar {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}
